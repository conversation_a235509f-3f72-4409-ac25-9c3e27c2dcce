import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './App.css'
import './index.css'
import './styles/global.css'

// Import components
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import LoadingSpinner from './components/LoadingSpinner';
import PageTransition from './components/PageTransition';
import { useAuth } from './contexts/AuthContext';

function App() {
  const { user, isLoading, logout } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 1024);
  const [activeMenuItem, setActiveMenuItem] = useState('dashboard');

  const location = useLocation();
  const navigate = useNavigate();

  // Update active menu item based on current route
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/dashboard')) {
      setActiveMenuItem('dashboard');
    } else if (path.includes('/users')) {
      setActiveMenuItem('users');
    } else if (path.includes('/courses')) {
      setActiveMenuItem('courses');
    } else if (path.includes('/quizzes')) {
      setActiveMenuItem('quizzes');
    } else if (path.includes('/analytics')) {
      setActiveMenuItem('analytics');
    } else if (path.includes('/upload')) {
      setActiveMenuItem('upload');
    } else if (path.includes('/students')) {
      setActiveMenuItem('students');
    } else if (path.includes('/progress')) {
      setActiveMenuItem('progress');
    } else if (path.includes('/recommendations')) {
      setActiveMenuItem('recommendations');
    } else if (path.includes('/settings')) {
      setActiveMenuItem('settings');
    }
  }, [location]);

  // Handle window resize for responsive sidebar
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsSidebarOpen(true);
      } else {
        setIsSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleSidebarClose = () => {
    setIsSidebarOpen(false);
  };

  const handleMenuItemClick = (item) => {
    setActiveMenuItem(item.id);
    navigate(item.path);
  };

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  // Mock notifications
  const notifications = [
    {
      id: 1,
      title: 'New Assignment',
      message: 'You have a new assignment in Mathematics',
      time: '2 hours ago',
      read: false
    },
    {
      id: 2,
      title: 'Grade Updated',
      message: 'Your grade for Physics Quiz has been updated',
      time: '1 day ago',
      read: true
    }
  ];

  // Show loading screen while initializing
  if (isLoading) {
    return (
      <LoadingSpinner
        fullScreen
        size="lg"
        text="Loading EduPlatform..."
      />
    );
  }

  // Check if current route should show layout (not login/register)
  const isAuthPage = location.pathname === '/login' || location.pathname === '/register';
  const shouldShowLayout = user && !isAuthPage;

  return (
    <div className="page-container">
      {shouldShowLayout && (
        <>
          {/* Navbar */}
          <Navbar
            user={user}
            onToggleSidebar={handleSidebarToggle}
            onLogout={handleLogout}
            notifications={notifications}
          />

          {/* Layout with Sidebar */}
          <div className="layout-container relative">
            <Sidebar
              isOpen={isSidebarOpen}
              userRole={user?.role}
              activeItem={activeMenuItem}
              onItemClick={handleMenuItemClick}
              onClose={handleSidebarClose}
            />

            {/* Main Content */}
            <main className="main-content">
              <div className="content-wrapper">
                <PageTransition>
                  <Outlet />
                </PageTransition>
              </div>
            </main>
          </div>
        </>
      )}

      {/* Auth pages without layout */}
      {!shouldShowLayout && (
        <div className="page-container">
          <PageTransition>
            <Outlet />
          </PageTransition>
        </div>
      )}

      {/* Toast notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />
    </div>
  );
}

export default App;
