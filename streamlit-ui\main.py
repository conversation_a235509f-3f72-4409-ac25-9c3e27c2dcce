import streamlit as st
from utils.auth import is_authenticated, get_user_role, logout

# Page configuration
st.set_page_config(
    page_title="Learning Management System",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .role-badge {
        background-color: #f0f2f6;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        color: #262730;
    }
    
    .stats-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin-bottom: 1rem;
    }
    
    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
        border: 1px solid #e1e5e9;
    }
    
    .feature-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🎓 Learning Management System</h1>
        <p>AI-Powered Education Platform</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Check authentication
    if not is_authenticated():
        show_welcome_page()
    else:
        show_dashboard()

def show_welcome_page():
    """Show welcome page for non-authenticated users"""
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("### Welcome to the LMS Platform")
        st.markdown("""
        Our Learning Management System offers:
        
        🤖 **AI-Powered Features**
        - Automated quiz generation using Gemini AI
        - Intelligent essay grading
        - Personalized course recommendations
        - Performance prediction using machine learning
        
        👥 **Role-Based Access**
        - **Students**: Take courses, quizzes, and track progress
        - **Professors**: Create courses, generate quizzes, grade essays
        - **Administrators**: Manage users and analyze performance
        
        📊 **Advanced Analytics**
        - Performance summaries and insights
        - Progress tracking and recommendations
        - Comprehensive reporting tools
        """)
        
        st.markdown("---")
        
        # Login/Register buttons
        col_login, col_register = st.columns(2)
        
        with col_login:
            if st.button("🔑 Login", use_container_width=True, type="primary"):
                st.switch_page("pages/login.py")
        
        with col_register:
            if st.button("📝 Register", use_container_width=True):
                st.switch_page("pages/register.py")
        
        # Demo credentials
        with st.expander("🔧 Demo Credentials"):
            st.markdown("""
            **Admin Account:**
            - Email: <EMAIL>
            - Password: admin123
            
            **Note:** You can create the admin account using the setup button below if it doesn't exist.
            """)
            
            if st.button("🛠️ Setup Admin Account"):
                try:
                    from services.api import api_client
                    result = api_client.create_admin()
                    st.success("Admin account created successfully!")
                    st.json(result)
                except Exception as e:
                    if "already exists" in str(e):
                        st.info("Admin account already exists")
                    else:
                        st.error(f"Error creating admin account: {e}")

def show_dashboard():
    """Show role-based dashboard"""
    user_role = get_user_role()
    
    # Sidebar
    with st.sidebar:
        st.markdown("### Navigation")
        
        # User info
        user = st.session_state.get("user", {})
        st.markdown(f"""
        **{user.get('full_name', 'User')}**  
        <span class="role-badge">{user_role.upper()}</span>
        """, unsafe_allow_html=True)
        
        st.markdown("---")
        
        # Role-specific navigation
        if user_role == "admin":
            show_admin_navigation()
        elif user_role == "professor":
            show_professor_navigation()
        elif user_role == "student":
            show_student_navigation()
        
        st.markdown("---")
        
        # Logout button
        if st.button("🚪 Logout", use_container_width=True):
            logout()
    
    # Main content
    if user_role == "admin":
        st.switch_page("pages/admin_dashboard.py")
    elif user_role == "professor":
        st.switch_page("pages/professor_dashboard.py")
    elif user_role == "student":
        st.switch_page("pages/student_dashboard.py")

def show_admin_navigation():
    """Show admin navigation menu"""
    if st.button("📊 Dashboard", use_container_width=True):
        st.switch_page("pages/admin_dashboard.py")
    
    if st.button("👥 Manage Users", use_container_width=True):
        st.switch_page("pages/admin_users.py")
    
    if st.button("📈 Performance Analysis", use_container_width=True):
        st.switch_page("pages/admin_performance.py")

def show_professor_navigation():
    """Show professor navigation menu"""
    if st.button("📊 Dashboard", use_container_width=True):
        st.switch_page("pages/professor_dashboard.py")
    
    if st.button("📚 My Courses", use_container_width=True):
        st.switch_page("pages/professor_courses.py")
    
    if st.button("❓ Quiz Generator", use_container_width=True):
        st.switch_page("pages/professor_quiz_generator.py")
    
    if st.button("📝 Essay Grader", use_container_width=True):
        st.switch_page("pages/professor_essay_grader.py")
    
    if st.button("🔮 Score Predictor", use_container_width=True):
        st.switch_page("pages/professor_score_predictor.py")

def show_student_navigation():
    """Show student navigation menu"""
    if st.button("📊 Dashboard", use_container_width=True):
        st.switch_page("pages/student_dashboard.py")
    
    if st.button("📚 My Courses", use_container_width=True):
        st.switch_page("pages/student_courses.py")
    
    if st.button("❓ Take Quiz", use_container_width=True):
        st.switch_page("pages/student_quiz.py")
    
    if st.button("📈 My Results", use_container_width=True):
        st.switch_page("pages/student_results.py")
    
    if st.button("💡 Recommendations", use_container_width=True):
        st.switch_page("pages/student_recommendations.py")

if __name__ == "__main__":
    main()
