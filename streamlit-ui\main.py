import streamlit as st
from utils.auth import is_authenticated, get_user_role, logout

# Page configuration
st.set_page_config(
    page_title="Learning Management System",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .role-badge {
        background-color: #f0f2f6;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        color: #262730;
    }
    
    .stats-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin-bottom: 1rem;
    }
    
    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
        border: 1px solid #e1e5e9;
    }
    
    .feature-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Initialize page state
    if "page" not in st.session_state:
        st.session_state.page = "home"

    # Check if user is authenticated and route accordingly
    if is_authenticated():
        # User is logged in - show role-based dashboard
        user_role = get_user_role()
        show_role_dashboard(user_role)
    else:
        # User not logged in - show authentication flow
        show_auth_flow()

def show_auth_flow():
    """Show authentication flow based on current page state"""
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🎓 Learning Management System</h1>
        <p>AI-Powered Education Platform</p>
    </div>
    """, unsafe_allow_html=True)

    # Page routing for authentication
    if st.session_state.page == "login":
        show_login_page()
    elif st.session_state.page == "register":
        show_register_page()
    else:
        show_welcome_page()

def show_welcome_page():
    """Show welcome page for non-authenticated users"""
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        st.markdown("### Welcome to the LMS Platform")
        st.markdown("""
        Our Learning Management System offers:

        🤖 **AI-Powered Features**
        - Automated quiz generation using Gemini AI
        - Intelligent essay grading
        - Personalized course recommendations
        - Performance prediction using machine learning

        👥 **Role-Based Access**
        - **Students**: Take courses, quizzes, and track progress
        - **Professors**: Create courses, generate quizzes, grade essays
        - **Administrators**: Manage users and analyze performance

        📊 **Advanced Analytics**
        - Performance summaries and insights
        - Progress tracking and recommendations
        - Comprehensive reporting tools
        """)

        st.markdown("---")

        # Login/Register buttons
        col_login, col_register = st.columns(2)

        with col_login:
            if st.button("🔑 Login", use_container_width=True, type="primary"):
                st.session_state.page = "login"
                st.rerun()

        with col_register:
            if st.button("📝 Register", use_container_width=True):
                st.session_state.page = "register"
                st.rerun()

        # Demo credentials
        with st.expander("🔧 Demo Credentials"):
            st.markdown("""
            **Admin Account:**
            - Email: <EMAIL>
            - Password: admin123

            **Professor Account:**
            - Email: <EMAIL>
            - Password: prof123

            **Student Account:**
            - Email: <EMAIL>
            - Password: student123

            **Note:** Create these accounts through registration if they don't exist.
            """)

            if st.button("🛠️ Setup Admin Account"):
                try:
                    from services.api import api_client
                    result = api_client.create_admin()
                    st.success("Admin account created successfully!")
                    st.json(result)
                except Exception as e:
                    if "already exists" in str(e):
                        st.info("Admin account already exists")
                    else:
                        st.error(f"Error creating admin account: {e}")

def show_login_page():
    """Show login page"""
    from services.api import api_client

    # Clean header for login page
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); border-radius: 10px; color: white; margin-bottom: 2rem;">
        <h1>🔑 Login to LMS</h1>
        <p>Enter your credentials to access your dashboard</p>
    </div>
    """, unsafe_allow_html=True)

    # Center the login form
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        with st.form("login_form"):
            email = st.text_input("📧 Email Address", placeholder="Enter your email")
            password = st.text_input("🔒 Password", type="password", placeholder="Enter your password")

            # Login button
            login_button = st.form_submit_button("🔑 Login", use_container_width=True, type="primary")

            # Navigation buttons
            col_reg, col_home = st.columns(2)
            with col_reg:
                if st.form_submit_button("📝 Register Instead", use_container_width=True):
                    st.session_state.page = "register"
                    st.rerun()

            with col_home:
                if st.form_submit_button("🏠 Back to Home", use_container_width=True):
                    st.session_state.page = "home"
                    st.rerun()

        # Handle login
        if login_button:
            if not email or not password:
                st.error("Please enter both email and password")
            else:
                try:
                    with st.spinner("Logging in..."):
                        response = api_client.login(email, password)

                    # Store authentication data
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]

                    # Show success message
                    st.success(f"✅ Welcome back, {response['user']['full_name']}!")
                    st.balloons()

                    # Small delay to show success message
                    import time
                    time.sleep(1)

                    # Redirect to role-based dashboard
                    st.rerun()

                except Exception as e:
                    st.error(f"❌ Login failed: {str(e)}")

        # Quick demo login buttons
        st.markdown("---")
        st.markdown("### 🚀 Quick Demo Login")

        col_admin, col_prof, col_student = st.columns(3)

        with col_admin:
            if st.button("👑 Login as Admin", use_container_width=True):
                try:
                    response = api_client.login("<EMAIL>", "admin123")
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]
                    st.success("Logged in as Admin!")
                    st.rerun()
                except Exception as e:
                    st.error("Admin account not found. Please create it first.")

        with col_prof:
            if st.button("👨‍🏫 Login as Professor", use_container_width=True):
                try:
                    response = api_client.login("<EMAIL>", "prof123")
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]
                    st.success("Logged in as Professor!")
                    st.rerun()
                except Exception as e:
                    st.error("Professor account not found. Please register first.")

        with col_student:
            if st.button("👨‍🎓 Login as Student", use_container_width=True):
                try:
                    response = api_client.login("<EMAIL>", "student123")
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]
                    st.success("Logged in as Student!")
                    st.rerun()
                except Exception as e:
                    st.error("Student account not found. Please register first.")

def show_register_page():
    """Show registration page"""
    from services.api import api_client

    # Clean header for register page
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(90deg, #28a745 0%, #20c997 100%); border-radius: 10px; color: white; margin-bottom: 2rem;">
        <h1>📝 Register for LMS</h1>
        <p>Create your account to start learning</p>
    </div>
    """, unsafe_allow_html=True)

    # Center the registration form
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        with st.form("register_form"):
            full_name = st.text_input("👤 Full Name", placeholder="Enter your full name")
            email = st.text_input("📧 Email Address", placeholder="Enter your email")
            password = st.text_input("🔒 Password", type="password", placeholder="Create a password")
            confirm_password = st.text_input("🔒 Confirm Password", type="password", placeholder="Confirm your password")

            role = st.selectbox("👥 Select your role", options=["student", "professor"], help="Choose your role in the system")

            # Role information
            if role == "student":
                st.info("👨‍🎓 **Student Role:** Enroll in courses, take quizzes, track progress, get recommendations")
            elif role == "professor":
                st.info("👨‍🏫 **Professor Role:** Create courses, generate AI quizzes, grade essays, predict performance")

            terms_accepted = st.checkbox("✅ I agree to the Terms of Service and Privacy Policy")

            # Register button
            register_button = st.form_submit_button("📝 Create Account", use_container_width=True, type="primary")

            # Navigation buttons
            col_login, col_home = st.columns(2)
            with col_login:
                if st.form_submit_button("🔑 Login Instead", use_container_width=True):
                    st.session_state.page = "login"
                    st.rerun()

            with col_home:
                if st.form_submit_button("🏠 Back to Home", use_container_width=True):
                    st.session_state.page = "home"
                    st.rerun()

        # Handle registration
        if register_button:
            errors = []

            if not full_name:
                errors.append("Full name is required")
            if not email or "@" not in email:
                errors.append("Valid email is required")
            if not password or len(password) < 6:
                errors.append("Password must be at least 6 characters")
            if password != confirm_password:
                errors.append("Passwords do not match")
            if not terms_accepted:
                errors.append("You must accept the terms")

            if errors:
                for error in errors:
                    st.error(f"❌ {error}")
            else:
                try:
                    with st.spinner("Creating your account..."):
                        user_data = {
                            "full_name": full_name,
                            "email": email,
                            "password": password,
                            "role": role
                        }
                        response = api_client.register(user_data)

                    # Store authentication data
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]

                    # Show success message
                    st.success(f"✅ Account created! Welcome, {full_name}!")
                    st.balloons()

                    # Small delay to show success message
                    import time
                    time.sleep(1)

                    # Redirect to role-based dashboard
                    st.rerun()

                except Exception as e:
                    st.error(f"❌ Registration failed: {str(e)}")

def show_role_dashboard(user_role):
    """Show role-based dashboard without sidebar"""
    user = st.session_state.get("user", {})

    # Top navigation bar with logout
    col1, col2, col3 = st.columns([3, 1, 1])

    with col1:
        st.markdown(f"""
        <div style="padding: 1rem; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); border-radius: 10px; color: white;">
            <h2>Welcome, {user.get('full_name', 'User')}!</h2>
            <p>Role: {user_role.upper()} | Dashboard</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        if st.button("🏠 Home", use_container_width=True):
            st.session_state.page = "home"
            st.rerun()

    with col3:
        if st.button("🚪 Logout", use_container_width=True, type="secondary"):
            logout()

    st.markdown("---")

    # Route to specific dashboard based on role
    if user_role == "admin":
        show_admin_dashboard_content()
    elif user_role == "professor":
        show_professor_dashboard_content()
    elif user_role == "student":
        show_student_dashboard_content()

def show_admin_dashboard_content():
    """Show admin dashboard content"""
    from services.api import api_client

    st.markdown("## 👑 Admin Dashboard")

    # Quick stats
    try:
        with st.spinner("Loading dashboard data..."):
            stats = api_client.get_admin_dashboard_stats()

        # Key metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_users = sum(stats["user_statistics"].values())
            st.metric("Total Users", total_users)

        with col2:
            total_courses = stats["course_statistics"]["total_courses"]
            st.metric("Total Courses", total_courses)

        with col3:
            total_attempts = stats["quiz_statistics"]["total_attempts"]
            st.metric("Quiz Attempts", total_attempts)

        with col4:
            new_users = stats["recent_activity"]["new_users_this_week"]
            st.metric("New Users (7d)", new_users)

        st.markdown("---")

        # Quick actions
        st.markdown("### ⚡ Quick Actions")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("👥 Manage Users", use_container_width=True, type="primary"):
                st.info("User management functionality - Create, edit, delete users")

        with col2:
            if st.button("📊 View All Courses", use_container_width=True, type="primary"):
                try:
                    courses = api_client.get_all_courses()
                    st.success(f"Found {len(courses)} courses in the system")
                    for course in courses[:5]:  # Show first 5
                        st.write(f"📚 {course['title']} - {course['professor_name']}")
                except Exception as e:
                    st.error(f"Error loading courses: {e}")

        with col3:
            if st.button("📈 Performance Analysis", use_container_width=True, type="primary"):
                st.info("Upload student performance data and generate AI insights")

    except Exception as e:
        st.error(f"Error loading admin dashboard: {e}")

def show_professor_dashboard_content():
    """Show professor dashboard content"""
    from services.api import api_client

    st.markdown("## 👨‍🏫 Professor Dashboard")

    # Quick stats
    try:
        with st.spinner("Loading your dashboard..."):
            stats = api_client.get_professor_dashboard_stats()

        # Key metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_courses = stats["course_statistics"]["total_courses"]
            st.metric("My Courses", total_courses)

        with col2:
            total_students = stats["course_statistics"]["total_enrolled_students"]
            st.metric("Total Students", total_students)

        with col3:
            total_quizzes = stats["quiz_statistics"]["total_quizzes"]
            st.metric("Quizzes Created", total_quizzes)

        with col4:
            total_attempts = stats["quiz_statistics"]["total_attempts"]
            st.metric("Quiz Attempts", total_attempts)

        st.markdown("---")

        # AI Tools
        st.markdown("### 🤖 AI-Powered Teaching Tools")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🧠 Generate Quiz", use_container_width=True, type="primary"):
                st.info("Use AI to generate quiz questions based on topics")

        with col2:
            if st.button("📝 Grade Essays", use_container_width=True, type="primary"):
                st.info("Automatically grade essays with detailed feedback")

        with col3:
            if st.button("🔮 Predict Scores", use_container_width=True, type="primary"):
                st.info("Predict student performance using ML models")

        # Course management
        st.markdown("### 📚 Course Management")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("➕ Create New Course", use_container_width=True, type="secondary"):
                st.info("Create a new course for your students")

        with col2:
            if st.button("📋 View My Courses", use_container_width=True, type="secondary"):
                try:
                    courses = api_client.get_professor_courses()
                    if courses:
                        st.success(f"You have {len(courses)} courses")
                        for course in courses:
                            st.write(f"📚 {course['title']} - {course['enrolled_count']} students")
                    else:
                        st.info("You haven't created any courses yet")
                except Exception as e:
                    st.error(f"Error loading courses: {e}")

    except Exception as e:
        st.error(f"Error loading professor dashboard: {e}")

def show_student_dashboard_content():
    """Show student dashboard content"""
    from services.api import api_client

    st.markdown("## 👨‍🎓 Student Dashboard")

    # Quick stats
    try:
        with st.spinner("Loading your dashboard..."):
            stats = api_client.get_student_dashboard_stats()

        # Key metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            enrolled_count = stats["enrollment_statistics"]["enrolled_courses"]
            st.metric("Enrolled Courses", enrolled_count)

        with col2:
            total_quizzes = stats["quiz_statistics"]["total_quizzes_taken"]
            st.metric("Quizzes Taken", total_quizzes)

        with col3:
            avg_score = stats["quiz_statistics"]["average_score"]
            st.metric("Average Score", f"{avg_score}%")

        with col4:
            highest_score = stats["quiz_statistics"]["highest_score"]
            st.metric("Best Score", f"{highest_score}%")

        st.markdown("---")

        # Quick actions
        st.markdown("### ⚡ Quick Actions")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔍 Browse Courses", use_container_width=True, type="primary"):
                try:
                    courses = api_client.get_available_courses()
                    st.success(f"Found {len(courses)} available courses")
                    for course in courses[:5]:  # Show first 5
                        st.write(f"📚 {course['title']} - {course['professor_name']}")
                except Exception as e:
                    st.error(f"Error loading courses: {e}")

        with col2:
            if st.button("📝 Take Quiz", use_container_width=True, type="primary"):
                st.info("Take quizzes from your enrolled courses")

        with col3:
            if st.button("💡 Get Recommendations", use_container_width=True, type="primary"):
                try:
                    recommendations = api_client.get_course_recommendations()
                    recs = recommendations.get("recommendations", [])
                    if recs:
                        st.success(f"Found {len(recs)} course recommendations for you!")
                        for rec in recs[:3]:  # Show first 3
                            st.write(f"💡 {rec['title']} - {rec['recommendation_reason']}")
                    else:
                        st.info("No recommendations available yet. Enroll in more courses!")
                except Exception as e:
                    st.error(f"Error getting recommendations: {e}")

        # My courses
        st.markdown("### 📚 My Enrolled Courses")

        try:
            enrolled_courses = api_client.get_enrolled_courses()
            if enrolled_courses:
                for course in enrolled_courses:
                    with st.expander(f"📚 {course['title']}"):
                        st.write(f"**Professor:** {course['professor_name']}")
                        st.write(f"**Category:** {course['category']}")
                        st.write(f"**Difficulty:** {course['difficulty_level'].title()}")
                        st.write(f"**Description:** {course['description']}")

                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button(f"📝 Take Quiz", key=f"quiz_{course['id']}"):
                                st.info("Quiz functionality coming soon!")
                        with col2:
                            if st.button(f"📊 View Progress", key=f"progress_{course['id']}"):
                                st.info("Progress tracking coming soon!")
            else:
                st.info("You haven't enrolled in any courses yet. Browse available courses to get started!")
        except Exception as e:
            st.error(f"Error loading enrolled courses: {e}")

    except Exception as e:
        st.error(f"Error loading student dashboard: {e}")

# Navigation functions removed - using direct dashboard content instead

if __name__ == "__main__":
    main()
