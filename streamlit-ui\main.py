import streamlit as st
from utils.auth import is_authenticated, get_user_role, logout

# Page configuration
st.set_page_config(
    page_title="Learning Management System",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .role-badge {
        background-color: #f0f2f6;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        color: #262730;
    }
    
    .stats-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin-bottom: 1rem;
    }
    
    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
        border: 1px solid #e1e5e9;
    }
    
    .feature-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Initialize page state
    if "page" not in st.session_state:
        st.session_state.page = "home"

    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🎓 Learning Management System</h1>
        <p>AI-Powered Education Platform</p>
    </div>
    """, unsafe_allow_html=True)

    # Page routing
    if st.session_state.page == "login":
        show_login_page()
    elif st.session_state.page == "register":
        show_register_page()
    elif is_authenticated():
        show_dashboard()
    else:
        show_welcome_page()

def show_welcome_page():
    """Show welcome page for non-authenticated users"""
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("### Welcome to the LMS Platform")
        st.markdown("""
        Our Learning Management System offers:
        
        🤖 **AI-Powered Features**
        - Automated quiz generation using Gemini AI
        - Intelligent essay grading
        - Personalized course recommendations
        - Performance prediction using machine learning
        
        👥 **Role-Based Access**
        - **Students**: Take courses, quizzes, and track progress
        - **Professors**: Create courses, generate quizzes, grade essays
        - **Administrators**: Manage users and analyze performance
        
        📊 **Advanced Analytics**
        - Performance summaries and insights
        - Progress tracking and recommendations
        - Comprehensive reporting tools
        """)
        
        st.markdown("---")
        
        # Login/Register buttons
        col_login, col_register = st.columns(2)
        
        with col_login:
            if st.button("🔑 Login", use_container_width=True, type="primary"):
                st.session_state.page = "login"
                st.rerun()

        with col_register:
            if st.button("📝 Register", use_container_width=True):
                st.session_state.page = "register"
                st.rerun()
        
        # Demo credentials
        with st.expander("🔧 Demo Credentials"):
            st.markdown("""
            **Admin Account:**
            - Email: <EMAIL>
            - Password: admin123
            
            **Note:** You can create the admin account using the setup button below if it doesn't exist.
            """)
            
            if st.button("🛠️ Setup Admin Account"):
                try:
                    from services.api import api_client
                    result = api_client.create_admin()
                    st.success("Admin account created successfully!")
                    st.json(result)
                except Exception as e:
                    if "already exists" in str(e):
                        st.info("Admin account already exists")
                    else:
                        st.error(f"Error creating admin account: {e}")

def show_login_page():
    """Show login page"""
    from services.api import api_client

    st.markdown("### 🔑 Login to LMS")

    with st.form("login_form"):
        email = st.text_input("Email Address", placeholder="Enter your email")
        password = st.text_input("Password", type="password", placeholder="Enter your password")

        col1, col2, col3 = st.columns([1, 1, 1])

        with col1:
            login_button = st.form_submit_button("🔑 Login", use_container_width=True, type="primary")

        with col2:
            if st.form_submit_button("📝 Register", use_container_width=True):
                st.session_state.page = "register"
                st.rerun()

        with col3:
            if st.form_submit_button("🏠 Home", use_container_width=True):
                st.session_state.page = "home"
                st.rerun()

    if login_button:
        if not email or not password:
            st.error("Please enter both email and password")
        else:
            try:
                with st.spinner("Logging in..."):
                    response = api_client.login(email, password)

                st.session_state.jwt_token = response["access_token"]
                st.session_state.user = response["user"]
                st.session_state.page = "dashboard"

                st.success(f"Welcome back, {response['user']['full_name']}!")
                st.rerun()

            except Exception as e:
                st.error(f"Login failed: {str(e)}")

def show_register_page():
    """Show registration page"""
    from services.api import api_client

    st.markdown("### 📝 Register for LMS")

    with st.form("register_form"):
        full_name = st.text_input("Full Name", placeholder="Enter your full name")
        email = st.text_input("Email Address", placeholder="Enter your email")
        password = st.text_input("Password", type="password", placeholder="Create a password")
        confirm_password = st.text_input("Confirm Password", type="password", placeholder="Confirm your password")
        role = st.selectbox("Select your role", options=["student", "professor"])
        terms_accepted = st.checkbox("I agree to the Terms of Service and Privacy Policy")

        col1, col2, col3 = st.columns([1, 1, 1])

        with col1:
            register_button = st.form_submit_button("📝 Create Account", use_container_width=True, type="primary")

        with col2:
            if st.form_submit_button("🔑 Login", use_container_width=True):
                st.session_state.page = "login"
                st.rerun()

        with col3:
            if st.form_submit_button("🏠 Home", use_container_width=True):
                st.session_state.page = "home"
                st.rerun()

    if register_button:
        errors = []

        if not full_name:
            errors.append("Full name is required")
        if not email or "@" not in email:
            errors.append("Valid email is required")
        if not password or len(password) < 6:
            errors.append("Password must be at least 6 characters")
        if password != confirm_password:
            errors.append("Passwords do not match")
        if not terms_accepted:
            errors.append("You must accept the terms")

        if errors:
            for error in errors:
                st.error(error)
        else:
            try:
                with st.spinner("Creating account..."):
                    user_data = {
                        "full_name": full_name,
                        "email": email,
                        "password": password,
                        "role": role
                    }
                    response = api_client.register(user_data)

                st.session_state.jwt_token = response["access_token"]
                st.session_state.user = response["user"]
                st.session_state.page = "dashboard"

                st.success(f"Account created! Welcome, {full_name}!")
                st.rerun()

            except Exception as e:
                st.error(f"Registration failed: {str(e)}")

def show_dashboard():
    """Show role-based dashboard"""
    user_role = get_user_role()

    # Sidebar
    with st.sidebar:
        st.markdown("### Navigation")

        # User info
        user = st.session_state.get("user", {})
        st.markdown(f"""
        **{user.get('full_name', 'User')}**
        <span class="role-badge">{user_role.upper()}</span>
        """, unsafe_allow_html=True)

        st.markdown("---")

        # Role-specific navigation
        if user_role == "admin":
            show_admin_navigation()
        elif user_role == "professor":
            show_professor_navigation()
        elif user_role == "student":
            show_student_navigation()

        st.markdown("---")

        # Logout button
        if st.button("🚪 Logout", use_container_width=True):
            logout()

    # Main content based on role
    if user_role == "admin":
        show_admin_dashboard_content()
    elif user_role == "professor":
        show_professor_dashboard_content()
    elif user_role == "student":
        show_student_dashboard_content()

def show_admin_dashboard_content():
    """Show admin dashboard content"""
    st.markdown("### 👑 Admin Dashboard")
    st.info("Admin dashboard functionality - manage users, view analytics, etc.")

def show_professor_dashboard_content():
    """Show professor dashboard content"""
    st.markdown("### 👨‍🏫 Professor Dashboard")
    st.info("Professor dashboard functionality - manage courses, create quizzes, etc.")

def show_student_dashboard_content():
    """Show student dashboard content"""
    st.markdown("### 👨‍🎓 Student Dashboard")
    st.info("Student dashboard functionality - view courses, take quizzes, etc.")

def show_admin_navigation():
    """Show admin navigation menu"""
    if st.button("📊 Dashboard", use_container_width=True):
        st.session_state.page = "admin_dashboard"
        st.rerun()

    if st.button("👥 Manage Users", use_container_width=True):
        st.session_state.page = "admin_users"
        st.rerun()

    if st.button("📈 Performance Analysis", use_container_width=True):
        st.session_state.page = "admin_performance"
        st.rerun()

def show_professor_navigation():
    """Show professor navigation menu"""
    if st.button("📊 Dashboard", use_container_width=True):
        st.session_state.page = "professor_dashboard"
        st.rerun()

    if st.button("📚 My Courses", use_container_width=True):
        st.session_state.page = "professor_courses"
        st.rerun()

    if st.button("❓ Quiz Generator", use_container_width=True):
        st.session_state.page = "professor_quiz_generator"
        st.rerun()

    if st.button("📝 Essay Grader", use_container_width=True):
        st.session_state.page = "professor_essay_grader"
        st.rerun()

    if st.button("🔮 Score Predictor", use_container_width=True):
        st.session_state.page = "professor_score_predictor"
        st.rerun()

def show_student_navigation():
    """Show student navigation menu"""
    if st.button("📊 Dashboard", use_container_width=True):
        st.session_state.page = "student_dashboard"
        st.rerun()

    if st.button("📚 My Courses", use_container_width=True):
        st.session_state.page = "student_courses"
        st.rerun()

    if st.button("❓ Take Quiz", use_container_width=True):
        st.session_state.page = "student_quiz"
        st.rerun()

    if st.button("📈 My Results", use_container_width=True):
        st.session_state.page = "student_results"
        st.rerun()

    if st.button("💡 Recommendations", use_container_width=True):
        st.session_state.page = "student_recommendations"
        st.rerun()

if __name__ == "__main__":
    main()
