/* Import Global Styles */
@import './styles/global.css';

/* ========================================
   PROJECT-SPECIFIC STYLES
   ======================================== */

/* Add any project-specific overrides or extensions here */

/* LMS-specific components */
.course-progress-ring {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.progress-circle {
  transform: rotate(-90deg);
  transition: stroke-dasharray 0.3s ease;
}

.quiz-option {
  padding: var(--space-4);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.quiz-option:hover {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.quiz-option.selected {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.quiz-option.correct {
  border-color: var(--color-success);
  background-color: var(--color-success-light);
}

.quiz-option.incorrect {
  border-color: var(--color-error);
  background-color: var(--color-error-light);
}

/* Sidebar specific styles */
.sidebar {
  width: 280px;
  background-color: var(--color-surface);
  border-right: 1px solid var(--color-border);
  transition: transform var(--transition-base);
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  color: var(--color-text);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  margin-bottom: var(--space-1);
}

.sidebar-item:hover {
  background-color: var(--color-surface-hover);
  transform: translateX(4px);
}

.sidebar-item.active {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.sidebar-item .icon {
  margin-right: var(--space-3);
  width: 20px;
  height: 20px;
}

/* Dashboard stats cards */
.stat-card {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  color: var(--color-text-inverse);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.stat-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: var(--text-sm);
  opacity: 0.9;
}

/* Course cards */
.course-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-base);
}

.course-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.course-image {
  height: 200px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  position: relative;
  overflow: hidden;
}

.course-content {
  padding: var(--space-6);
}

.course-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-2);
  color: var(--color-text);
}

.course-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
}

.course-progress {
  width: 100%;
  height: 8px;
  background-color: var(--color-background-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-4);
}

.course-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: var(--radius-full);
  transition: width 0.5s ease;
}

/* Assignment cards */
.assignment-card {
  padding: var(--space-6);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.assignment-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.assignment-status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.assignment-status.pending {
  background-color: var(--color-warning-light);
  color: var(--color-warning);
}

.assignment-status.completed {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.assignment-status.overdue {
  background-color: var(--color-error-light);
  color: var(--color-error);
}

/* File upload area */
.file-upload-zone {
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-12);
  text-align: center;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.file-upload-zone:hover,
.file-upload-zone.dragover {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.file-upload-icon {
  font-size: var(--text-5xl);
  color: var(--color-text-tertiary);
  margin-bottom: var(--space-4);
}

/* Navigation */
.navbar {
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.navbar-brand {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-primary);
}

/* Mobile responsive adjustments */
@media (max-width: 1023px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: var(--z-fixed);
    transform: translateX(-100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: var(--z-modal-backdrop);
  }
}

/* Dark theme adjustments for LMS components */
[data-theme='dark'] .course-image {
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
}

[data-theme='dark'] .file-upload-zone:hover,
[data-theme='dark'] .file-upload-zone.dragover {
  background-color: rgba(79, 70, 229, 0.1);
}

/* Loading states for LMS */
.course-skeleton {
  background-color: var(--color-background-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.course-skeleton .skeleton-image {
  height: 200px;
  background-color: var(--color-background-tertiary);
}

.course-skeleton .skeleton-content {
  padding: var(--space-6);
}

.course-skeleton .skeleton-line {
  height: 16px;
  background-color: var(--color-background-tertiary);
  border-radius: var(--radius-base);
  margin-bottom: var(--space-3);
}

.course-skeleton .skeleton-line:nth-child(1) {
  width: 80%;
}

.course-skeleton .skeleton-line:nth-child(2) {
  width: 100%;
}

.course-skeleton .skeleton-line:nth-child(3) {
  width: 60%;
}

/* Animation for skeleton loading */
@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.skeleton-line {
  animation: skeleton-loading 1.5s ease-in-out infinite;
}
