import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRoute, faCheck, faTimes } from '@fortawesome/free-solid-svg-icons';

const RouteTest = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // All routes to test
  const routes = {
    'Authentication': [
      { path: '/login', name: 'Login Page' },
      { path: '/register', name: 'Register Page' },
      { path: '/unauthorized', name: 'Unauthorized Page' }
    ],
    'Admin Routes': [
      { path: '/admin/dashboard', name: 'Admin Dashboard' },
      { path: '/admin/users', name: 'User Management' },
      { path: '/admin/courses', name: 'Course Management' },
      { path: '/admin/analytics', name: 'System Analytics' },
      { path: '/admin/settings', name: 'System Settings' }
    ],
    'Professor Routes': [
      { path: '/professor/dashboard', name: 'Professor Dash<PERSON>' },
      { path: '/professor/courses', name: 'My Courses' },
      { path: '/professor/upload', name: 'Upload Lecture' },
      { path: '/professor/quizzes', name: 'Manage Quizzes' },
      { path: '/professor/students', name: 'Student Performance' },
      { path: '/professor/analytics', name: 'Course Analytics' }
    ],
    'Student Routes': [
      { path: '/student/dashboard', name: 'Student Dashboard' },
      { path: '/student/courses', name: 'My Courses' },
      { path: '/student/quizzes', name: 'Take Quizzes' },
      { path: '/student/progress', name: 'My Progress' },
      { path: '/student/recommendations', name: 'Recommendations' }
    ]
  };

  const testRoute = (path) => {
    try {
      navigate(path);
      return true;
    } catch (error) {
      console.error(`Failed to navigate to ${path}:`, error);
      return false;
    }
  };

  const isCurrentRoute = (path) => {
    return location.pathname === path;
  };

  return (
    <motion.div
      className="p-8 max-w-6xl mx-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-base-content mb-4">
          <FontAwesomeIcon icon={faRoute} className="mr-3 text-primary" />
          Route Testing Dashboard
        </h1>
        <p className="text-base-content/60">
          Test all application routes and verify navigation works correctly
        </p>
        <div className="mt-4 p-4 bg-info/10 border border-info/20 rounded-lg">
          <p className="text-sm text-info">
            <strong>Current Route:</strong> {location.pathname}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {Object.entries(routes).map(([category, categoryRoutes]) => (
          <motion.div
            key={category}
            className="card-custom p-6"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <h2 className="text-xl font-semibold text-base-content mb-4 border-b border-base-300 pb-2">
              {category}
            </h2>
            <div className="space-y-3">
              {categoryRoutes.map((route) => (
                <motion.div
                  key={route.path}
                  className={`
                    flex items-center justify-between p-3 rounded-lg border transition-all duration-200
                    ${isCurrentRoute(route.path) 
                      ? 'bg-primary/10 border-primary/30' 
                      : 'bg-base-200 border-base-300 hover:bg-base-300'
                    }
                  `}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`
                      w-3 h-3 rounded-full
                      ${isCurrentRoute(route.path) ? 'bg-primary' : 'bg-base-400'}
                    `} />
                    <div>
                      <p className="font-medium text-base-content">{route.name}</p>
                      <p className="text-xs text-base-content/60 font-mono">{route.path}</p>
                    </div>
                  </div>
                  <button
                    className={`
                      btn btn-sm
                      ${isCurrentRoute(route.path) 
                        ? 'btn-success' 
                        : 'btn-primary'
                      }
                    `}
                    onClick={() => testRoute(route.path)}
                    disabled={isCurrentRoute(route.path)}
                  >
                    {isCurrentRoute(route.path) ? (
                      <>
                        <FontAwesomeIcon icon={faCheck} className="mr-1" />
                        Current
                      </>
                    ) : (
                      'Test'
                    )}
                  </button>
                </motion.div>
              ))}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Quick Navigation */}
      <motion.div
        className="mt-8 card-custom p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h2 className="text-xl font-semibold text-base-content mb-4">Quick Navigation</h2>
        <div className="flex flex-wrap gap-2">
          <button
            className="btn btn-primary btn-sm"
            onClick={() => navigate('/')}
          >
            Home
          </button>
          <button
            className="btn btn-secondary btn-sm"
            onClick={() => navigate('/login')}
          >
            Login
          </button>
          <button
            className="btn btn-accent btn-sm"
            onClick={() => navigate('/admin/dashboard')}
          >
            Admin
          </button>
          <button
            className="btn btn-info btn-sm"
            onClick={() => navigate('/professor/dashboard')}
          >
            Professor
          </button>
          <button
            className="btn btn-success btn-sm"
            onClick={() => navigate('/student/dashboard')}
          >
            Student
          </button>
          <button
            className="btn btn-warning btn-sm"
            onClick={() => navigate('/nonexistent')}
          >
            Test 404
          </button>
        </div>
      </motion.div>

      {/* Route Status */}
      <motion.div
        className="mt-8 card-custom p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <h2 className="text-xl font-semibold text-base-content mb-4">Route Status</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="stat bg-success/10 border border-success/20 rounded-lg p-4">
            <div className="stat-title text-success">Total Routes</div>
            <div className="stat-value text-success">
              {Object.values(routes).flat().length}
            </div>
          </div>
          <div className="stat bg-info/10 border border-info/20 rounded-lg p-4">
            <div className="stat-title text-info">Categories</div>
            <div className="stat-value text-info">
              {Object.keys(routes).length}
            </div>
          </div>
          <div className="stat bg-warning/10 border border-warning/20 rounded-lg p-4">
            <div className="stat-title text-warning">Current Path</div>
            <div className="stat-value text-warning text-sm font-mono">
              {location.pathname}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default RouteTest;
