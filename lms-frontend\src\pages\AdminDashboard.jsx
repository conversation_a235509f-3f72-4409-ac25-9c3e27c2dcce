import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faGraduationCap,
  faBook,
  faChartLine,
  faPlus,
  faEdit,
  faTrash,
  faEye,
  faSearch,
  faFilter,
  faDownload
} from '@fortawesome/free-solid-svg-icons';
import { toast } from 'react-toastify';

// Import components
import { StatCard, UserCard } from '../components/Card';
import ProgressChart, { LineProgressChart, PieProgressChart } from '../components/ProgressChart';
import AlertModal, { ConfirmDeleteModal, useModal } from '../components/AlertModal';
import LoadingSpinner, { TableSkeleton } from '../components/LoadingSpinner';

const AdminDashboard = () => {
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState(null);
  
  // Modal states
  const deleteModal = useModal();
  const editModal = useModal();
  const viewModal = useModal();

  // Mock data
  const mockStats = {
    totalUsers: 1247,
    totalCourses: 89,
    totalStudents: 1089,
    totalProfessors: 67,
    activeUsers: 892,
    newUsersThisMonth: 156
  };

  const mockAnalyticsData = [
    { name: 'Jan', users: 400, courses: 20 },
    { name: 'Feb', users: 500, courses: 25 },
    { name: 'Mar', users: 600, courses: 30 },
    { name: 'Apr', users: 750, courses: 35 },
    { name: 'May', users: 900, courses: 45 },
    { name: 'Jun', users: 1100, courses: 55 },
    { name: 'Jul', users: 1247, courses: 89 }
  ];

  const mockUserDistribution = [
    { name: 'Students', value: 1089, color: '#3b82f6' },
    { name: 'Professors', value: 67, color: '#10b981' },
    { name: 'Admins', value: 91, color: '#f59e0b' }
  ];

  const mockUsers = [
    {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'student',
      avatar: null,
      status: 'active',
      joinDate: '2024-01-15',
      lastLogin: '2024-07-12'
    },
    {
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'professor',
      avatar: null,
      status: 'active',
      joinDate: '2023-09-20',
      lastLogin: '2024-07-13'
    },
    {
      id: 3,
      name: 'Mike Johnson',
      email: '<EMAIL>',
      role: 'admin',
      avatar: null,
      status: 'inactive',
      joinDate: '2023-05-10',
      lastLogin: '2024-07-10'
    },
    {
      id: 4,
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      role: 'student',
      avatar: null,
      status: 'active',
      joinDate: '2024-03-08',
      lastLogin: '2024-07-13'
    },
    {
      id: 5,
      name: 'David Brown',
      email: '<EMAIL>',
      role: 'professor',
      avatar: null,
      status: 'active',
      joinDate: '2023-11-12',
      lastLogin: '2024-07-12'
    }
  ];

  // Load data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setUsers(mockUsers);
      setFilteredUsers(mockUsers);
      setIsLoading(false);
    };

    loadData();
  }, []);

  // Filter users based on search and role
  useEffect(() => {
    let filtered = users;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by role
    if (selectedRole !== 'all') {
      filtered = filtered.filter(user => user.role === selectedRole);
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, selectedRole]);

  // User actions
  const handleViewUser = (user) => {
    setSelectedUser(user);
    viewModal.openModal();
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    editModal.openModal();
  };

  const handleDeleteUser = (user) => {
    setSelectedUser(user);
    deleteModal.openModal();
  };

  const confirmDeleteUser = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUsers(prev => prev.filter(u => u.id !== selectedUser.id));
      toast.success(`User ${selectedUser.name} deleted successfully`);
      deleteModal.closeModal();
      setSelectedUser(null);
    } catch (error) {
      toast.error('Failed to delete user');
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="stat-card animate-pulse">
              <div className="h-16 bg-base-300 rounded"></div>
            </div>
          ))}
        </div>
        <TableSkeleton rows={5} columns={6} />
      </div>
    );
  }

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-base-content mb-2">Admin Dashboard</h1>
        <p className="text-base-content/60">Manage users, courses, and system analytics</p>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        className="stats-grid"
        variants={itemVariants}
      >
        <StatCard
          title="Total Users"
          value={mockStats.totalUsers.toLocaleString()}
          icon={faUsers}
          change="+12.5%"
          changeType="positive"
        />
        <StatCard
          title="Total Courses"
          value={mockStats.totalCourses}
          icon={faBook}
          change="+8.2%"
          changeType="positive"
        />
        <StatCard
          title="Active Users"
          value={mockStats.activeUsers.toLocaleString()}
          icon={faChartLine}
          change="+5.1%"
          changeType="positive"
        />
        <StatCard
          title="New This Month"
          value={mockStats.newUsersThisMonth}
          icon={faGraduationCap}
          change="+18.7%"
          changeType="positive"
        />
      </motion.div>

      {/* Analytics Charts */}
      <motion.div
        className="responsive-grid"
        variants={itemVariants}
      >
        <LineProgressChart
          data={mockAnalyticsData}
          dataKey="users"
          xAxisKey="name"
          title="User Growth Over Time"
          color="#3b82f6"
          height={300}
        />
        <PieProgressChart
          data={mockUserDistribution}
          title="User Distribution by Role"
          height={300}
        />
      </motion.div>

      {/* User Management Section */}
      <motion.div variants={itemVariants}>
        <div className="bg-base-100 rounded-xl shadow-md border border-base-300">
          {/* Header */}
          <div className="p-6 border-b border-base-300">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h2 className="text-xl font-semibold text-base-content">User Management</h2>
                <p className="text-base-content/60">Manage all system users</p>
              </div>
              <button className="btn btn-primary">
                <FontAwesomeIcon icon={faPlus} className="mr-2" />
                Add User
              </button>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mt-4">
              <div className="relative flex-1">
                <FontAwesomeIcon 
                  icon={faSearch} 
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-base-content/40"
                />
                <input
                  type="text"
                  placeholder="Search users..."
                  className="input input-bordered w-full pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <select
                className="select select-bordered"
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
              >
                <option value="all">All Roles</option>
                <option value="student">Students</option>
                <option value="professor">Professors</option>
                <option value="admin">Admins</option>
              </select>
              <button className="btn btn-ghost">
                <FontAwesomeIcon icon={faDownload} className="mr-2" />
                Export
              </button>
            </div>
          </div>

          {/* User List */}
          <div className="p-6">
            {filteredUsers.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-base-content/60">No users found</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredUsers.map((user) => (
                  <UserCard
                    key={user.id}
                    user={user}
                    onView={handleViewUser}
                    onEdit={handleEditUser}
                    onDelete={handleDeleteUser}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </motion.div>

      {/* Modals */}
      <ConfirmDeleteModal
        isOpen={deleteModal.isOpen}
        onClose={deleteModal.closeModal}
        onConfirm={confirmDeleteUser}
        itemName={selectedUser?.name}
      />

      {/* View User Modal */}
      <AlertModal
        isOpen={viewModal.isOpen}
        onClose={viewModal.closeModal}
        onConfirm={viewModal.closeModal}
        title="User Details"
        confirmText="Close"
        showCancel={false}
        size="lg"
        message={
          selectedUser && (
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-white text-xl font-bold">
                    {selectedUser.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{selectedUser.name}</h3>
                  <p className="text-base-content/60">{selectedUser.email}</p>
                  <span className={`badge ${
                    selectedUser.role === 'admin' ? 'badge-error' :
                    selectedUser.role === 'professor' ? 'badge-warning' : 'badge-info'
                  }`}>
                    {selectedUser.role}
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Status:</span>
                  <span className={`ml-2 ${selectedUser.status === 'active' ? 'text-success' : 'text-error'}`}>
                    {selectedUser.status}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Join Date:</span>
                  <span className="ml-2">{selectedUser.joinDate}</span>
                </div>
                <div>
                  <span className="font-medium">Last Login:</span>
                  <span className="ml-2">{selectedUser.lastLogin}</span>
                </div>
              </div>
            </div>
          )
        }
      />
    </motion.div>
  );
};

export default AdminDashboard;
