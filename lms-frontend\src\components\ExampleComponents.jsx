import React from 'react';

const ExampleComponents = () => {
  return (
    <div className="content-wrapper">
      <div className="fade-in">
        <h1 className="text-3xl font-bold mb-6 text-gradient">LMS Global CSS Examples</h1>
        
        {/* Stats Grid */}
        <div className="stats-grid mb-8">
          <div className="stat-card">
            <h3 className="text-lg font-semibold mb-2">Total Students</h3>
            <p className="text-3xl font-bold">1,234</p>
            <p className="text-sm opacity-80">+12% from last month</p>
          </div>
          <div className="stat-card">
            <h3 className="text-lg font-semibold mb-2">Active Courses</h3>
            <p className="text-3xl font-bold">56</p>
            <p className="text-sm opacity-80">+3 new courses</p>
          </div>
          <div className="stat-card">
            <h3 className="text-lg font-semibold mb-2">Completion Rate</h3>
            <p className="text-3xl font-bold">89%</p>
            <p className="text-sm opacity-80">+5% improvement</p>
          </div>
          <div className="stat-card">
            <h3 className="text-lg font-semibold mb-2">Total Quizzes</h3>
            <p className="text-3xl font-bold">342</p>
            <p className="text-sm opacity-80">+28 this week</p>
          </div>
        </div>

        {/* Course Grid */}
        <h2 className="text-2xl font-bold mb-4">Recent Courses</h2>
        <div className="course-grid mb-8">
          <div className="course-card hover-lift">
            <div className="h-48 bg-gradient-to-br from-blue-500 to-purple-600 rounded-t-xl"></div>
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-2">React Development</h3>
              <p className="text-base-content/70 mb-4">Learn modern React development with hooks and context.</p>
              <div className="course-progress mb-3">
                <div className="course-progress-bar" style={{width: '75%'}}></div>
              </div>
              <div className="flex justify-between items-center">
                <span className="badge-custom bg-primary text-primary-content">75% Complete</span>
                <button className="btn btn-primary btn-sm">Continue</button>
              </div>
            </div>
          </div>
          
          <div className="course-card hover-lift">
            <div className="h-48 bg-gradient-to-br from-green-500 to-teal-600 rounded-t-xl"></div>
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-2">Node.js Backend</h3>
              <p className="text-base-content/70 mb-4">Build robust backend APIs with Node.js and Express.</p>
              <div className="course-progress mb-3">
                <div className="course-progress-bar" style={{width: '45%'}}></div>
              </div>
              <div className="flex justify-between items-center">
                <span className="badge-custom bg-secondary text-secondary-content">45% Complete</span>
                <button className="btn btn-primary btn-sm">Continue</button>
              </div>
            </div>
          </div>
          
          <div className="course-card hover-lift">
            <div className="h-48 bg-gradient-to-br from-orange-500 to-red-600 rounded-t-xl"></div>
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-2">Database Design</h3>
              <p className="text-base-content/70 mb-4">Master SQL and NoSQL database design principles.</p>
              <div className="course-progress mb-3">
                <div className="course-progress-bar" style={{width: '90%'}}></div>
              </div>
              <div className="flex justify-between items-center">
                <span className="badge-custom bg-success text-success-content">90% Complete</span>
                <button className="btn btn-primary btn-sm">Continue</button>
              </div>
            </div>
          </div>
        </div>

        {/* Dashboard Cards */}
        <h2 className="text-2xl font-bold mb-4">Dashboard Overview</h2>
        <div className="dashboard-grid mb-8">
          <div className="dashboard-card">
            <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm">Completed React Quiz #3</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-secondary rounded-full"></div>
                <span className="text-sm">Started Node.js Course</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span className="text-sm">Submitted Assignment #2</span>
              </div>
            </div>
          </div>
          
          <div className="dashboard-card">
            <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
            <div className="space-y-2">
              <button className="btn btn-primary-custom w-full">Start New Course</button>
              <button className="btn btn-secondary-custom w-full">Take Quiz</button>
              <button className="btn btn-ghost-custom w-full">View Progress</button>
            </div>
          </div>
        </div>

        {/* Form Example */}
        <h2 className="text-2xl font-bold mb-4">Form Components</h2>
        <div className="form-container mb-8">
          <h3 className="text-xl font-semibold mb-4">Contact Form</h3>
          <form className="space-y-4">
            <div className="form-group">
              <label className="form-label">Full Name</label>
              <input type="text" className="input-custom" placeholder="Enter your name" />
            </div>
            <div className="form-group">
              <label className="form-label">Email</label>
              <input type="email" className="input-custom" placeholder="Enter your email" />
              <div className="form-help">We'll never share your email address.</div>
            </div>
            <div className="form-group">
              <label className="form-label">Message</label>
              <textarea className="input-custom h-24" placeholder="Enter your message"></textarea>
            </div>
            <button type="submit" className="btn btn-primary-custom w-full">Send Message</button>
          </form>
        </div>

        {/* Alerts */}
        <h2 className="text-2xl font-bold mb-4">Alert Components</h2>
        <div className="space-y-4 mb-8">
          <div className="alert alert-success">
            <span>Your assignment has been submitted successfully!</span>
          </div>
          <div className="alert alert-warning">
            <span>Quiz deadline is approaching in 2 hours.</span>
          </div>
          <div className="alert alert-error">
            <span>Failed to upload file. Please try again.</span>
          </div>
          <div className="alert alert-info">
            <span>New course materials are now available.</span>
          </div>
        </div>

        {/* File Upload Area */}
        <h2 className="text-2xl font-bold mb-4">File Upload</h2>
        <div className="file-upload-area mb-8">
          <div className="text-center">
            <div className="text-4xl mb-4">📁</div>
            <p className="text-lg font-medium mb-2">Drop files here or click to browse</p>
            <p className="text-sm text-base-content/60">Supports PDF, DOC, DOCX (max 10MB)</p>
          </div>
        </div>

        {/* Table Example */}
        <h2 className="text-2xl font-bold mb-4">Data Table</h2>
        <div className="overflow-x-auto mb-8">
          <table className="table-custom">
            <thead>
              <tr>
                <th>Course</th>
                <th>Progress</th>
                <th>Grade</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>React Development</td>
                <td>
                  <div className="progress-custom">
                    <div className="progress-bar" style={{width: '75%'}}></div>
                  </div>
                </td>
                <td>A-</td>
                <td><span className="badge-custom bg-success text-success-content">Active</span></td>
              </tr>
              <tr>
                <td>Node.js Backend</td>
                <td>
                  <div className="progress-custom">
                    <div className="progress-bar" style={{width: '45%'}}></div>
                  </div>
                </td>
                <td>B+</td>
                <td><span className="badge-custom bg-warning text-warning-content">In Progress</span></td>
              </tr>
              <tr>
                <td>Database Design</td>
                <td>
                  <div className="progress-custom">
                    <div className="progress-bar" style={{width: '90%'}}></div>
                  </div>
                </td>
                <td>A</td>
                <td><span className="badge-custom bg-success text-success-content">Completed</span></td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Loading States */}
        <h2 className="text-2xl font-bold mb-4">Loading States</h2>
        <div className="space-y-4 mb-8">
          <div className="flex items-center space-x-4">
            <div className="loading-spinner"></div>
            <span>Loading...</span>
          </div>
          
          <div className="space-y-2">
            <div className="skeleton h-4 w-3/4"></div>
            <div className="skeleton h-4 w-1/2"></div>
            <div className="skeleton h-4 w-2/3"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExampleComponents;
