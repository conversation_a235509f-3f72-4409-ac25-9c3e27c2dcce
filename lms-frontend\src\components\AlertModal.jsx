import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faExclamationTriangle,
  faInfoCircle,
  faCheckCircle,
  faTimesCircle,
  faTimes,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';

const AlertModal = ({
  isOpen = false,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  type = 'warning', // success, error, warning, info
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  showCancel = true,
  isLoading = false,
  size = 'md', // sm, md, lg
  className = ''
}) => {
  // Icon and color mapping based on type
  const typeConfig = {
    success: {
      icon: faCheckCircle,
      iconColor: 'text-success',
      bgColor: 'bg-success/10',
      borderColor: 'border-success/20',
      buttonClass: 'btn-success'
    },
    error: {
      icon: faTimesCircle,
      iconColor: 'text-error',
      bgColor: 'bg-error/10',
      borderColor: 'border-error/20',
      buttonClass: 'btn-error'
    },
    warning: {
      icon: faExclamationTriangle,
      iconColor: 'text-warning',
      bgColor: 'bg-warning/10',
      borderColor: 'border-warning/20',
      buttonClass: 'btn-warning'
    },
    info: {
      icon: faInfoCircle,
      iconColor: 'text-info',
      bgColor: 'bg-info/10',
      borderColor: 'border-info/20',
      buttonClass: 'btn-info'
    }
  };

  const config = typeConfig[type];

  // Size classes
  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg'
  };

  // Animation variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  const modalVariants = {
    hidden: {
      opacity: 0,
      scale: 0.8,
      y: 50
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: 50,
      transition: {
        duration: 0.2
      }
    }
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && !isLoading) {
      onClose();
    }
  };

  const handleConfirm = () => {
    if (onConfirm && !isLoading) {
      onConfirm();
    }
  };

  const handleCancel = () => {
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          onClick={handleOverlayClick}
        >
          {/* Backdrop */}
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

          {/* Modal */}
          <motion.div
            className={`
              relative bg-base-100 rounded-xl shadow-2xl border border-base-300 w-full
              ${sizeClasses[size]} ${className}
            `}
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {/* Close button */}
            <button
              className="absolute top-4 right-4 btn btn-ghost btn-sm btn-circle"
              onClick={handleCancel}
              disabled={isLoading}
            >
              <FontAwesomeIcon icon={faTimes} />
            </button>

            {/* Content */}
            <div className="p-6">
              {/* Icon and Title */}
              <div className="flex items-center mb-4">
                <div className={`
                  flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center mr-4
                  ${config.bgColor} ${config.borderColor} border
                `}>
                  <FontAwesomeIcon 
                    icon={config.icon} 
                    className={`text-xl ${config.iconColor}`}
                  />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-base-content">
                    {title}
                  </h3>
                </div>
              </div>

              {/* Message */}
              <div className="mb-6">
                {typeof message === 'string' ? (
                  <p className="text-base-content/70 leading-relaxed">
                    {message}
                  </p>
                ) : (
                  message
                )}
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3">
                {showCancel && (
                  <motion.button
                    className="btn btn-ghost"
                    onClick={handleCancel}
                    disabled={isLoading}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {cancelText}
                  </motion.button>
                )}
                
                <motion.button
                  className={`btn ${config.buttonClass}`}
                  onClick={handleConfirm}
                  disabled={isLoading}
                  whileHover={{ scale: isLoading ? 1 : 1.05 }}
                  whileTap={{ scale: isLoading ? 1 : 0.95 }}
                >
                  {isLoading ? (
                    <>
                      <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    confirmText
                  )}
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Specialized alert modals
export const ConfirmDeleteModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  itemName = 'item',
  isLoading = false 
}) => {
  return (
    <AlertModal
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onConfirm}
      type="error"
      title="Delete Confirmation"
      message={
        <div>
          <p className="mb-2">
            Are you sure you want to delete <strong>{itemName}</strong>?
          </p>
          <p className="text-sm text-error/70">
            This action cannot be undone.
          </p>
        </div>
      }
      confirmText="Delete"
      cancelText="Cancel"
      isLoading={isLoading}
    />
  );
};

export const SuccessModal = ({ 
  isOpen, 
  onClose, 
  title = 'Success!', 
  message = 'Operation completed successfully.' 
}) => {
  return (
    <AlertModal
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onClose}
      type="success"
      title={title}
      message={message}
      confirmText="OK"
      showCancel={false}
    />
  );
};

export const ErrorModal = ({ 
  isOpen, 
  onClose, 
  title = 'Error', 
  message = 'An error occurred. Please try again.' 
}) => {
  return (
    <AlertModal
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onClose}
      type="error"
      title={title}
      message={message}
      confirmText="OK"
      showCancel={false}
    />
  );
};

export const InfoModal = ({ 
  isOpen, 
  onClose, 
  title = 'Information', 
  message,
  onConfirm
}) => {
  return (
    <AlertModal
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onConfirm || onClose}
      type="info"
      title={title}
      message={message}
      confirmText="OK"
      showCancel={false}
    />
  );
};

// Custom hook for managing modal state
export const useModal = (initialState = false) => {
  const [isOpen, setIsOpen] = React.useState(initialState);

  const openModal = React.useCallback(() => setIsOpen(true), []);
  const closeModal = React.useCallback(() => setIsOpen(false), []);
  const toggleModal = React.useCallback(() => setIsOpen(prev => !prev), []);

  return {
    isOpen,
    openModal,
    closeModal,
    toggleModal
  };
};

export default AlertModal;
