import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faBook,
  faUsers,
  faQuestionCircle,
  faChartLine,
  faPlus,
  faUpload,
  faEdit,
  faTrash,
  faEye,
  faDownload,
  faPlay,
  faClock,
  faGraduationCap
} from '@fortawesome/free-solid-svg-icons';
import { toast } from 'react-toastify';

// Import components
import { StatCard, CourseCard } from '../components/Card';
import ProgressChart, { BarProgressChart, LineProgressChart } from '../components/ProgressChart';
import AlertModal, { ConfirmDeleteModal, useModal } from '../components/AlertModal';
import LoadingSpinner, { CardSkeleton } from '../components/LoadingSpinner';

const ProfessorDashboard = () => {
  const [courses, setCourses] = useState([]);
  const [quizzes, setQuizzes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  
  // Modal states
  const deleteModal = useModal();
  const uploadModal = useModal();

  // Mock data
  const mockStats = {
    totalCourses: 8,
    totalStudents: 342,
    totalQuizzes: 24,
    avgRating: 4.7
  };

  const mockPerformanceData = [
    { name: 'Math 101', students: 45, avgScore: 85 },
    { name: 'Physics 201', students: 38, avgScore: 78 },
    { name: 'Chemistry 301', students: 32, avgScore: 82 },
    { name: 'Biology 101', students: 52, avgScore: 88 },
    { name: 'Statistics', students: 28, avgScore: 76 }
  ];

  const mockEngagementData = [
    { name: 'Jan', engagement: 78 },
    { name: 'Feb', engagement: 82 },
    { name: 'Mar', engagement: 85 },
    { name: 'Apr', engagement: 79 },
    { name: 'May', engagement: 88 },
    { name: 'Jun', engagement: 92 },
    { name: 'Jul', engagement: 89 }
  ];

  const mockCourses = [
    {
      id: 1,
      title: 'Introduction to Mathematics',
      description: 'Basic mathematical concepts and problem-solving techniques',
      instructor: 'Prof. Johnson',
      students: 45,
      duration: '12 weeks',
      level: 'Beginner',
      image: 'https://via.placeholder.com/300x200?text=Math+101',
      status: 'active',
      progress: 75
    },
    {
      id: 2,
      title: 'Advanced Physics',
      description: 'Quantum mechanics and modern physics concepts',
      instructor: 'Prof. Johnson',
      students: 38,
      duration: '16 weeks',
      level: 'Advanced',
      image: 'https://via.placeholder.com/300x200?text=Physics+201',
      status: 'active',
      progress: 60
    },
    {
      id: 3,
      title: 'Organic Chemistry',
      description: 'Chemical reactions and molecular structures',
      instructor: 'Prof. Johnson',
      students: 32,
      duration: '14 weeks',
      level: 'Intermediate',
      image: 'https://via.placeholder.com/300x200?text=Chemistry+301',
      status: 'draft',
      progress: 30
    }
  ];

  const mockQuizzes = [
    {
      id: 1,
      title: 'Algebra Fundamentals',
      course: 'Introduction to Mathematics',
      questions: 15,
      duration: 30,
      attempts: 42,
      avgScore: 85,
      status: 'active',
      dueDate: '2024-07-20'
    },
    {
      id: 2,
      title: 'Quantum Mechanics Quiz',
      course: 'Advanced Physics',
      questions: 20,
      duration: 45,
      attempts: 35,
      avgScore: 78,
      status: 'active',
      dueDate: '2024-07-25'
    },
    {
      id: 3,
      title: 'Chemical Bonds',
      course: 'Organic Chemistry',
      questions: 12,
      duration: 25,
      attempts: 0,
      avgScore: 0,
      status: 'draft',
      dueDate: '2024-08-01'
    }
  ];

  // Load data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setCourses(mockCourses);
      setQuizzes(mockQuizzes);
      setIsLoading(false);
    };

    loadData();
  }, []);

  // Actions
  const handleViewCourse = (course) => {
    console.log('View course:', course);
    toast.info(`Viewing ${course.title}`);
  };

  const handleEditCourse = (course) => {
    console.log('Edit course:', course);
    toast.info(`Editing ${course.title}`);
  };

  const handleDeleteItem = (item, type) => {
    setSelectedItem({ ...item, type });
    deleteModal.openModal();
  };

  const confirmDelete = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (selectedItem.type === 'course') {
        setCourses(prev => prev.filter(c => c.id !== selectedItem.id));
        toast.success(`Course "${selectedItem.title}" deleted successfully`);
      } else {
        setQuizzes(prev => prev.filter(q => q.id !== selectedItem.id));
        toast.success(`Quiz "${selectedItem.title}" deleted successfully`);
      }
      
      deleteModal.closeModal();
      setSelectedItem(null);
    } catch (error) {
      toast.error('Failed to delete item');
    }
  };

  const handleUploadLecture = () => {
    uploadModal.openModal();
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="stat-card animate-pulse">
              <div className="h-16 bg-base-300 rounded"></div>
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <CardSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-base-content mb-2">Professor Dashboard</h1>
        <p className="text-base-content/60">Manage your courses, quizzes, and track student performance</p>
      </motion.div>

      {/* Stats Cards */}
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        variants={itemVariants}
      >
        <StatCard
          title="My Courses"
          value={mockStats.totalCourses}
          icon={faBook}
          change="+2"
          changeType="positive"
        />
        <StatCard
          title="Total Students"
          value={mockStats.totalStudents}
          icon={faUsers}
          change="+15"
          changeType="positive"
        />
        <StatCard
          title="Active Quizzes"
          value={mockStats.totalQuizzes}
          icon={faQuestionCircle}
          change="+3"
          changeType="positive"
        />
        <StatCard
          title="Avg Rating"
          value={mockStats.avgRating}
          icon={faGraduationCap}
          change="+0.2"
          changeType="positive"
        />
      </motion.div>

      {/* Quick Actions */}
      <motion.div variants={itemVariants}>
        <div className="flex flex-wrap gap-4">
          <button 
            className="btn btn-primary"
            onClick={handleUploadLecture}
          >
            <FontAwesomeIcon icon={faUpload} className="mr-2" />
            Upload Lecture
          </button>
          <button className="btn btn-secondary">
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Create Quiz
          </button>
          <button className="btn btn-accent">
            <FontAwesomeIcon icon={faBook} className="mr-2" />
            New Course
          </button>
          <button className="btn btn-ghost">
            <FontAwesomeIcon icon={faDownload} className="mr-2" />
            Export Reports
          </button>
        </div>
      </motion.div>

      {/* Analytics Charts */}
      <motion.div 
        className="grid grid-cols-1 lg:grid-cols-2 gap-6"
        variants={itemVariants}
      >
        <BarProgressChart
          data={mockPerformanceData}
          dataKey="avgScore"
          xAxisKey="name"
          title="Course Performance (Average Scores)"
          color="#10b981"
          height={300}
        />
        <LineProgressChart
          data={mockEngagementData}
          dataKey="engagement"
          xAxisKey="name"
          title="Student Engagement Over Time"
          color="#3b82f6"
          height={300}
        />
      </motion.div>

      {/* Tabs */}
      <motion.div variants={itemVariants}>
        <div className="tabs tabs-boxed bg-base-200 p-1">
          <button 
            className={`tab ${activeTab === 'overview' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button 
            className={`tab ${activeTab === 'courses' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('courses')}
          >
            My Courses
          </button>
          <button 
            className={`tab ${activeTab === 'quizzes' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('quizzes')}
          >
            Quizzes
          </button>
        </div>
      </motion.div>

      {/* Tab Content */}
      <motion.div variants={itemVariants}>
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Recent Courses */}
            <div className="col-span-full lg:col-span-2">
              <h3 className="text-xl font-semibold mb-4">Recent Courses</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {courses.slice(0, 4).map((course) => (
                  <CourseCard
                    key={course.id}
                    course={course}
                    onView={handleViewCourse}
                    onEnroll={handleEditCourse}
                    enrolled={true}
                  />
                ))}
              </div>
            </div>

            {/* Recent Activity */}
            <div>
              <h3 className="text-xl font-semibold mb-4">Recent Activity</h3>
              <div className="space-y-3">
                <div className="p-3 bg-base-100 rounded-lg border border-base-300">
                  <p className="text-sm font-medium">New student enrolled</p>
                  <p className="text-xs text-base-content/60">Math 101 - 2 hours ago</p>
                </div>
                <div className="p-3 bg-base-100 rounded-lg border border-base-300">
                  <p className="text-sm font-medium">Quiz submitted</p>
                  <p className="text-xs text-base-content/60">Physics 201 - 4 hours ago</p>
                </div>
                <div className="p-3 bg-base-100 rounded-lg border border-base-300">
                  <p className="text-sm font-medium">Course updated</p>
                  <p className="text-xs text-base-content/60">Chemistry 301 - 1 day ago</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'courses' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold">My Courses</h3>
              <button className="btn btn-primary">
                <FontAwesomeIcon icon={faPlus} className="mr-2" />
                Create Course
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {courses.map((course) => (
                <div key={course.id} className="card-custom">
                  <img 
                    src={course.image} 
                    alt={course.title}
                    className="w-full h-48 object-cover rounded-lg mb-4"
                  />
                  <h4 className="text-lg font-semibold mb-2">{course.title}</h4>
                  <p className="text-base-content/60 text-sm mb-4">{course.description}</p>
                  
                  <div className="flex items-center justify-between text-sm text-base-content/60 mb-4">
                    <span>{course.students} students</span>
                    <span className={`badge ${
                      course.status === 'active' ? 'badge-success' : 'badge-warning'
                    }`}>
                      {course.status}
                    </span>
                  </div>

                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>{course.progress}%</span>
                    </div>
                    <div className="w-full bg-base-300 rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${course.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button 
                      className="btn btn-sm btn-primary flex-1"
                      onClick={() => handleViewCourse(course)}
                    >
                      <FontAwesomeIcon icon={faEye} className="mr-1" />
                      View
                    </button>
                    <button 
                      className="btn btn-sm btn-ghost"
                      onClick={() => handleEditCourse(course)}
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                    <button 
                      className="btn btn-sm btn-error"
                      onClick={() => handleDeleteItem(course, 'course')}
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'quizzes' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold">Quiz Management</h3>
              <button className="btn btn-primary">
                <FontAwesomeIcon icon={faPlus} className="mr-2" />
                Create Quiz
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="table w-full">
                <thead>
                  <tr>
                    <th>Quiz Title</th>
                    <th>Course</th>
                    <th>Questions</th>
                    <th>Duration</th>
                    <th>Attempts</th>
                    <th>Avg Score</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {quizzes.map((quiz) => (
                    <tr key={quiz.id}>
                      <td className="font-medium">{quiz.title}</td>
                      <td>{quiz.course}</td>
                      <td>{quiz.questions}</td>
                      <td>
                        <FontAwesomeIcon icon={faClock} className="mr-1" />
                        {quiz.duration}min
                      </td>
                      <td>{quiz.attempts}</td>
                      <td>{quiz.avgScore}%</td>
                      <td>
                        <span className={`badge ${
                          quiz.status === 'active' ? 'badge-success' : 'badge-warning'
                        }`}>
                          {quiz.status}
                        </span>
                      </td>
                      <td>
                        <div className="flex space-x-1">
                          <button className="btn btn-xs btn-primary">
                            <FontAwesomeIcon icon={faEye} />
                          </button>
                          <button className="btn btn-xs btn-ghost">
                            <FontAwesomeIcon icon={faEdit} />
                          </button>
                          <button 
                            className="btn btn-xs btn-error"
                            onClick={() => handleDeleteItem(quiz, 'quiz')}
                          >
                            <FontAwesomeIcon icon={faTrash} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </motion.div>

      {/* Modals */}
      <ConfirmDeleteModal
        isOpen={deleteModal.isOpen}
        onClose={deleteModal.closeModal}
        onConfirm={confirmDelete}
        itemName={selectedItem?.title}
      />

      {/* Upload Lecture Modal */}
      <AlertModal
        isOpen={uploadModal.isOpen}
        onClose={uploadModal.closeModal}
        onConfirm={() => {
          toast.success('Lecture upload feature coming soon!');
          uploadModal.closeModal();
        }}
        title="Upload Lecture"
        confirmText="Upload"
        message={
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Select Course</label>
              <select className="select select-bordered w-full">
                <option>Choose a course...</option>
                {courses.map(course => (
                  <option key={course.id}>{course.title}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Lecture Title</label>
              <input type="text" className="input input-bordered w-full" placeholder="Enter lecture title" />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Upload File</label>
              <input type="file" className="file-input file-input-bordered w-full" />
            </div>
          </div>
        }
      />
    </motion.div>
  );
};

export default ProfessorDashboard;
